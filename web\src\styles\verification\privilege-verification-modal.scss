/**
 * 特权验证流程弹框样式
 */

.privilege-verification-modal {
  padding: 1rem 0;

  .verification-steps {
    margin-bottom: 2rem;
  }

  .step-content {
    min-height: 300px;

    .step-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: var(--text-color-1);
      margin-bottom: 0.5rem;
    }

    .step-description {
      color: var(--text-color-2);
      margin-bottom: 1.5rem;
      line-height: 1.5;
    }

    .step-actions {
      display: flex;
      justify-content: flex-end;
      gap: 0.75rem;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid var(--border-color);
    }
  }

  // 步骤一样式
  .step-one {
    .verification-info {
      margin-bottom: 1.5rem;
      padding: 1rem;
      background-color: var(--card-color);
      border-radius: 0.5rem;
      border: 1px solid var(--border-color);

      .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;

        &:last-child {
          margin-bottom: 0;
        }

        .info-label {
          font-weight: 500;
          color: var(--text-color-2);
          min-width: 5rem;
          flex-shrink: 0;
        }

        .info-value {
          color: var(--text-color-1);
          flex: 1;
        }
      }
    }

    .template-link-section {
      margin-bottom: 1.5rem;
      padding: 1rem;
      background-color: var(--info-color-suppl);
      border-radius: 0.5rem;
      border: 1px solid var(--info-color);

      .link-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        font-weight: 500;
        color: var(--text-color-1);

        .icon {
          font-size: 1.125rem;
        }
      }

      .link-container {
        margin-bottom: 0.75rem;

        .template-link-button {
          width: 100%;
          height: 2.5rem;
        }
      }

      .link-description {
        font-size: 0.875rem;
        color: var(--text-color-2);
        line-height: 1.4;
      }
    }

    .qrcode-upload-section {
      margin-bottom: 1.5rem;
      padding: 1rem;
      background-color: var(--warning-color-suppl);
      border-radius: 0.5rem;
      border: 1px solid var(--warning-color);

      .upload-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;
        font-weight: 500;
        color: var(--text-color-1);

        .icon {
          font-size: 1.125rem;
        }
      }

      .qrcode-upload {
        .upload-placeholder {
          text-align: center;
          padding: 2rem 1rem;

          .upload-icon {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
          }

          .upload-text {
            font-size: 1rem;
            font-weight: 500;
            color: var(--text-color-1);
            margin-bottom: 0.25rem;
          }

          .upload-hint {
            font-size: 0.875rem;
            color: var(--text-color-3);
          }
        }

        .upload-preview {
          text-align: center;
          padding: 1rem;

          .qrcode-preview-image {
            max-width: 200px;
            max-height: 200px;
            border-radius: 0.5rem;
            margin-bottom: 0.5rem;
          }

          .upload-success {
            font-size: 0.875rem;
            color: var(--success-color);
            font-weight: 500;
          }
        }
      }
    }

    .start-button-hint {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.5rem;
      padding: 0.75rem 1rem;
      background-color: var(--warning-color-suppl);
      border-radius: 0.375rem;
      font-size: 0.875rem;
      color: var(--text-color-2);

      .hint-icon {
        font-size: 1rem;
      }
    }
  }

  // 步骤二样式
  .step-two {
    .remaining-time {
      display: flex;
      justify-content: center;
      margin-bottom: 1rem;
    }

    .verification-input {
      margin-bottom: 1rem;
    }
  }

  // 步骤三样式
  .step-three {
    .verification-status {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 200px;
      text-align: center;

      .status-progress,
      .status-success,
      .status-failed,
      .status-timeout {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 1rem;
      }

      .status-icon {
        font-size: 3rem;
        font-weight: bold;
        border-radius: 50%;
        width: 4rem;
        height: 4rem;
        display: flex;
        align-items: center;
        justify-content: center;

        &.success-icon {
          color: #18a058;
          background-color: rgba(24, 160, 88, 0.1);
        }

        &.failed-icon {
          color: #d03050;
          background-color: rgba(208, 48, 80, 0.1);
        }

        &.timeout-icon {
          color: #f0a020;
          background-color: rgba(240, 160, 32, 0.1);
        }
      }

      .status-text {
        font-size: 1.125rem;
        font-weight: 500;
        color: var(--text-color-1);
      }

      .status-description {
        font-size: 0.875rem;
        color: var(--text-color-2);
      }

      .remaining-time {
        font-size: 0.875rem;
        color: var(--text-color-3);
        margin-top: 0.5rem;
      }
    }
  }
}

// 响应式设计
@media (width <= 768px) {
  .privilege-verification-modal {
    .step-content {
      min-height: 250px;
    }

    .step-actions {
      flex-direction: column-reverse;
      gap: 0.5rem;

      .n-button {
        width: 100%;
      }
    }
  }
}

// 深色主题适配
[data-theme='dark'] {
  .privilege-verification-modal {
    .step-title {
      color: var(--text-color-1);
    }

    .step-description {
      color: var(--text-color-2);
    }

    .verification-option {
      .option-title {
        color: var(--text-color-1);
      }

      .option-description {
        color: var(--text-color-3);
      }
    }

    .status-text {
      color: var(--text-color-1);
    }

    .status-description {
      color: var(--text-color-2);
    }
  }
}
