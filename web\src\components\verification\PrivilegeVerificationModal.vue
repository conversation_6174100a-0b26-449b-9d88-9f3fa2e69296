<!--
  特权验证流程弹框组件
  
  功能说明：
  - 使用NaiveUI步骤组件显示验证流程
  - 支持三个步骤的验证流程
  - 实时显示验证状态和剩余时间
  - 支持不同验证类型的处理
-->
<template>
  <NModal
    v-model:show="visible"
    preset="dialog"
    title="特权验证"
    :mask-closable="false"
    :closable="!verification.isLoading && !verification.isSubmitting"
    style="width: 600px"
    @close="handleClose"
  >
    <div class="privilege-verification-modal">
      <!-- 步骤指示器 -->
      <NSteps
        :current="verification.currentStep.value"
        :status="getStepStatus()"
        class="verification-steps"
      >
        <NStep
          v-for="step in steps"
          :key="step.value"
          :title="step.title"
          :description="step.description"
        />
      </NSteps>

      <!-- 步骤内容 -->
      <div class="step-content">
        <!-- 步骤一：选择验证类型并启动 -->
        <div v-if="verification.currentStep.value === 1" class="step-one">
          <div class="step-title">启动验证流程</div>
          <div class="step-description">
            即将为特权「{{ privilegeName }}」启动{{ getVerificationTypeLabel() }}验证流程
          </div>

          <div class="verification-info">
            <div class="info-item">
              <span class="info-label">特权名称：</span>
              <span class="info-value">{{ privilegeName }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">验证方式：</span>
              <span class="info-value">{{ getVerificationTypeLabel() }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">验证说明：</span>
              <span class="info-value">{{ getVerificationTypeDescription() }}</span>
            </div>
          </div>

          <!-- 模板平台链接 -->
          <div v-if="props.privilege?.link" class="template-link-section">
            <div class="link-title">
              <span class="icon">🔗</span>
              <strong>请先访问对应的模板平台：</strong>
            </div>
            <div class="link-container">
              <NButton
                type="info"
                tag="a"
                :href="props.privilege.link"
                target="_blank"
                @click="handleLinkClick"
                class="template-link-button"
              >
                <template #icon>
                  <span>🌐</span>
                </template>
                访问 {{ privilegeName }} 平台
              </NButton>
            </div>
            <div class="link-description">
              <span v-if="props.privilege?.verificationType === 0">
                请在平台上发送验证码，然后返回此页面继续验证流程
              </span>
              <span v-else>
                请在平台上截图登录二维码，然后返回此页面继续验证流程
              </span>
            </div>
          </div>

          <!-- 二维码图片上传（仅二维码验证类型显示） -->
          <div v-if="props.privilege?.verificationType === 1 && linkClicked" class="qrcode-upload-section">
            <div class="upload-title">
              <span class="icon">📱</span>
              <strong>上传二维码截图：</strong>
            </div>
            <NUpload
              :max="1"
              accept="image/*"
              :show-file-list="false"
              @change="handleQrCodeUpload"
              class="qrcode-upload"
            >
              <NUploadDragger>
                <div v-if="!qrCodeImage" class="upload-placeholder">
                  <div class="upload-icon">📷</div>
                  <div class="upload-text">点击或拖拽上传二维码截图</div>
                  <div class="upload-hint">支持 JPG、PNG 格式</div>
                </div>
                <div v-else class="upload-preview">
                  <img :src="qrCodeImageUrl" alt="二维码预览" class="qrcode-preview-image" />
                  <div class="upload-success">✅ 二维码截图已上传</div>
                </div>
              </NUploadDragger>
            </NUpload>
          </div>

          <div class="step-actions">
            <NButton @click="handleClose" :disabled="verification.isLoading.value"> 取消 </NButton>
            <NButton
              v-if="shouldShowStartButton"
              type="primary"
              @click="handleStartVerification"
              :loading="verification.isLoading.value"
            >
              开始验证
            </NButton>
            <div v-else class="start-button-hint">
              <span class="hint-icon">💡</span>
              <span v-if="!linkClicked">请先点击上方链接访问模板平台</span>
              <span v-else-if="props.privilege?.verificationType === 1 && !qrCodeImage">
                请上传二维码截图后继续
              </span>
            </div>
          </div>
        </div>

        <!-- 步骤二：提交验证内容 -->
        <div v-else-if="verification.currentStep.value === 2" class="step-two">
          <div class="step-title">提交验证内容</div>
          <div class="step-description">
            {{ getVerificationDescription() }}
          </div>

          <!-- 剩余时间显示 -->
          <div v-if="verification.remainingTime.value > 0" class="remaining-time">
            <NTag type="warning" size="large">
              剩余时间：{{ verification.formatRemainingTime(verification.remainingTime.value) }}
            </NTag>
          </div>

          <!-- 验证内容输入 -->
          <div class="verification-input">
            <NInput
              v-model:value="verificationContent"
              type="textarea"
              :placeholder="getInputPlaceholder()"
              :rows="4"
              :disabled="verification.isSubmitting.value"
              show-count
              :maxlength="500"
            />
          </div>

          <div class="step-actions">
            <NButton @click="handleClose" :disabled="verification.isSubmitting.value">
              取消
            </NButton>
            <NButton
              type="primary"
              @click="handleSubmitContent"
              :loading="verification.isSubmitting.value"
              :disabled="!verificationContent.trim()"
            >
              提交验证
            </NButton>
          </div>
        </div>

        <!-- 步骤三：等待验证完成 -->
        <div v-else-if="verification.currentStep.value === 3" class="step-three">
          <div class="step-title">等待验证完成</div>
          <div class="step-description">验证内容已提交，请等待验证完成</div>

          <!-- 验证状态显示 -->
          <div class="verification-status">
            <div v-if="verification.isInProgress.value" class="status-progress">
              <NSpin size="large" />
              <div class="status-text">验证进行中...</div>
              <div v-if="verification.remainingTime.value > 0" class="remaining-time">
                剩余时间：{{ verification.formatRemainingTime(verification.remainingTime.value) }}
              </div>
            </div>

            <div v-else-if="verification.isCompleted.value" class="status-success">
              <div class="status-icon success-icon">✓</div>
              <div class="status-text">验证成功！</div>
              <div class="status-description">特权已成功获取</div>
            </div>

            <div v-else-if="verification.isFailed.value" class="status-failed">
              <div class="status-icon failed-icon">✗</div>
              <div class="status-text">验证失败</div>
              <div class="status-description">请重新尝试验证</div>
            </div>

            <div v-else-if="verification.isTimeout.value" class="status-timeout">
              <div class="status-icon timeout-icon">⏰</div>
              <div class="status-text">验证超时</div>
              <div class="status-description">验证时间已过期</div>
            </div>
          </div>

          <div class="step-actions">
            <NButton
              v-if="
                verification.isCompleted.value ||
                verification.isFailed.value ||
                verification.isTimeout.value
              "
              type="primary"
              @click="handleClose"
            >
              确定
            </NButton>
            <NButton v-else @click="handleClose"> 取消 </NButton>
          </div>
        </div>
      </div>
    </div>
  </NModal>
</template>

<script lang="ts" setup>
import { NButton, NInput, NModal, NSpin, NStep, NSteps, NTag, NUpload, NUploadDragger, type UploadFileInfo } from 'naive-ui'
import { computed, ref, watch } from 'vue'

import { usePrivilegeVerification } from '@/composables/verification/usePrivilegeVerification'
import { VERIFICATION_TYPE_LABELS } from '@/constants/privilege/verification-type.constants'
import {
  VERIFICATION_STEP_DESCRIPTIONS,
  VERIFICATION_STEP_LABELS,
} from '@/constants/verification/verification-step.constants'
import type { PrivilegeResponse } from '@/types/privilege/privilege-response.types'

interface Props {
  modelValue: boolean
  privilege: PrivilegeResponse | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 使用验证流程管理
const verification = usePrivilegeVerification()

// 弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 表单数据
const verificationContent = ref('')

// 步骤1相关状态
const linkClicked = ref(false)
const qrCodeImage = ref<File | null>(null)
const qrCodeImageUrl = ref('')

// 计算属性
const privilegeName = computed(() => props.privilege?.name || '')

// 是否显示开始验证按钮
const shouldShowStartButton = computed(() => {
  if (!linkClicked.value) return false
  if (props.privilege?.verificationType === 1 && !qrCodeImage.value) return false
  return true
})

// 步骤配置
const steps = [
  {
    value: 1,
    title: VERIFICATION_STEP_LABELS[1],
    description: VERIFICATION_STEP_DESCRIPTIONS[1],
  },
  {
    value: 2,
    title: VERIFICATION_STEP_LABELS[2],
    description: VERIFICATION_STEP_DESCRIPTIONS[2],
  },
  {
    value: 3,
    title: VERIFICATION_STEP_LABELS[3],
    description: VERIFICATION_STEP_DESCRIPTIONS[3],
  },
]

/**
 * 获取步骤状态
 */
const getStepStatus = () => {
  if (verification.isCompleted.value) return 'finish'
  if (verification.isFailed.value || verification.isTimeout.value) return 'error'
  if (verification.isInProgress.value) return 'process'
  return 'wait'
}

/**
 * 获取验证类型标签
 */
const getVerificationTypeLabel = (): string => {
  if (!props.privilege) return ''

  const type = props.privilege.verificationType
  return VERIFICATION_TYPE_LABELS[type as keyof typeof VERIFICATION_TYPE_LABELS] || '未知验证类型'
}

/**
 * 获取验证类型描述
 */
const getVerificationTypeDescription = (): string => {
  if (!props.privilege) return ''

  const type = props.privilege.verificationType

  if (type === 0) {
    return '通过短信接收验证码进行验证'
  } else if (type === 1) {
    return '通过扫描二维码进行验证'
  } else {
    return '未知验证方式'
  }
}

/**
 * 获取验证描述
 */
const getVerificationDescription = (): string => {
  if (!verification.verificationData.value) return ''

  const type = verification.verificationData.value.verificationType
  const typeLabel = VERIFICATION_TYPE_LABELS[type as keyof typeof VERIFICATION_TYPE_LABELS]

  if (type === 0) {
    return `请输入通过${typeLabel}收到的验证码或相关信息`
  } else {
    return `请输入通过${typeLabel}获取的验证信息`
  }
}

/**
 * 获取输入框占位符
 */
const getInputPlaceholder = (): string => {
  if (!verification.verificationData.value) return '请输入验证内容'

  const type = verification.verificationData.value.verificationType

  if (type === 0) {
    return '请输入短信验证码'
  } else {
    return '请输入二维码验证信息'
  }
}

/**
 * 处理链接点击
 */
const handleLinkClick = (): void => {
  linkClicked.value = true
}

/**
 * 处理二维码图片上传
 */
const handleQrCodeUpload = (options: { fileList: UploadFileInfo[] }): void => {
  if (options.fileList.length > 0) {
    const file = options.fileList[0]
    if (file.file) {
      qrCodeImage.value = file.file
      // 创建预览URL
      qrCodeImageUrl.value = URL.createObjectURL(file.file)
    }
  } else {
    qrCodeImage.value = null
    if (qrCodeImageUrl.value) {
      URL.revokeObjectURL(qrCodeImageUrl.value)
      qrCodeImageUrl.value = ''
    }
  }
}

/**
 * 开始验证流程
 */
const handleStartVerification = async (): Promise<void> => {
  if (!props.privilege) {
    return
  }

  const success = await verification.startVerification({
    privilegeId: props.privilege.id,
    verificationType: props.privilege.verificationType as 0 | 1,
  })

  if (!success) {
    // 验证启动失败，保持在步骤一
    return
  }
}

/**
 * 提交验证内容
 */
const handleSubmitContent = async (): Promise<void> => {
  if (!verificationContent.value.trim()) {
    return
  }

  const success = await verification.submitVerificationContent(verificationContent.value.trim())

  if (success) {
    // 提交成功，清空输入内容
    verificationContent.value = ''
  }
}

/**
 * 关闭弹框
 */
const handleClose = (): void => {
  if (verification.isCompleted.value) {
    emit('success')
  }

  // 重置状态
  resetModal()
  visible.value = false
}

/**
 * 重置弹框状态
 */
const resetModal = (): void => {
  verificationContent.value = ''
  linkClicked.value = false
  qrCodeImage.value = null
  if (qrCodeImageUrl.value) {
    URL.revokeObjectURL(qrCodeImageUrl.value)
    qrCodeImageUrl.value = ''
  }
  verification.resetVerification()
}

// 监听弹框显示状态
watch(visible, (newVisible) => {
  if (!newVisible) {
    // 弹框关闭时重置状态
    resetModal()
  }
})
</script>

<style lang="scss" scoped>
@use '@/styles/verification/privilege-verification-modal';
</style>
