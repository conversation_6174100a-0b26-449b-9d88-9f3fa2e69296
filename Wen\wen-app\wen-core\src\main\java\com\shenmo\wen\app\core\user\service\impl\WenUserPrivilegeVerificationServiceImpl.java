package com.shenmo.wen.app.core.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;

import com.shenmo.wen.app.core.config.properties.PrivilegeVerificationProperties;
import com.shenmo.wen.app.core.user.constant.PrivilegeVerificationConstant;
import com.shenmo.wen.app.core.user.enums.PrivilegeVerificationStatus;
import com.shenmo.wen.app.core.user.enums.PrivilegeVerificationStep;
import com.shenmo.wen.app.core.user.enums.PrivilegeVerificationType;
import com.shenmo.wen.app.core.user.exception.UserException;
import com.shenmo.wen.app.core.user.exception.UserExceptionEnum;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeMapper;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeTemplateMapper;
import com.shenmo.wen.app.core.user.mapper.WenUserPrivilegeVerificationMapper;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilege;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeTemplate;
import com.shenmo.wen.app.core.user.pojo.entity.WenUserPrivilegeVerification;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationStartReq;
import com.shenmo.wen.app.core.user.pojo.req.WenUserPrivilegeVerificationSubmitReq;
import com.shenmo.wen.app.core.user.pojo.resp.WenVerificationTimeInfoResp;
import com.shenmo.wen.app.core.user.pojo.resp.WenUserPrivilegeVerificationResp;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeVerificationService;
import com.shenmo.wen.app.core.user.service.WenUserPrivilegeVerificationEmailService;
import com.shenmo.wen.common.constant.BucketConstant;
import com.shenmo.wen.common.objectstorage.template.ObjectStorageTemplate;
import com.shenmo.wen.common.util.EnCodingUtils;
import com.shenmo.wen.common.util.HtmlTemplateUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.security.SecureRandom;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 用户特权验证服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenUserPrivilegeVerificationServiceImpl implements WenUserPrivilegeVerificationService {

    private final WenUserPrivilegeVerificationMapper verificationMapper;
    private final WenUserPrivilegeMapper privilegeMapper;
    private final WenUserPrivilegeTemplateMapper templateMapper;
    private final WenUserMapper userMapper;
    private final PrivilegeVerificationProperties properties;
    private final ObjectStorageTemplate objectStorageTemplate;
    private final WenUserPrivilegeVerificationEmailService emailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public WenUserPrivilegeVerificationResp startVerification(WenUserPrivilegeVerificationStartReq req) {
        final long loginId = StpUtil.getLoginIdAsLong();
        final Long privilegeId = req.getPrivilegeId();
        final Integer verificationType = req.getVerificationType();

        // 验证用户存在
        if (!userMapper.existsById(loginId)) {
            throw new UserException(UserExceptionEnum.USER_NOT_EXISTS);
        }

        // 验证特权存在
        final WenUserPrivilege privilege = privilegeMapper.selectById(privilegeId);
        if (privilege == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
        }

        // 检查今日是否已申请过
        if (verificationMapper.existsTodayApplication(loginId, privilegeId)) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_DAILY_LIMIT_EXCEEDED);
        }

        // 验证验证类型是否匹配特权类型
        if (!verificationType.equals(privilege.getVerificationType())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 创建验证流程
        final WenUserPrivilegeVerification verification = createVerificationRecord(loginId, privilegeId,
                verificationType);

        // 执行步骤一逻辑
        executeStepOne(verification, privilege);

        return toResp(verification);
    }

    /**
     * 创建验证流程记录
     */
    private WenUserPrivilegeVerification createVerificationRecord(Long userId, Long privilegeId,
            Integer verificationType) {
        final WenUserPrivilegeVerification verification = new WenUserPrivilegeVerification();
        verification.setUserId(userId);
        verification.setPrivilegeId(privilegeId);
        verification.setCurrentStep(PrivilegeVerificationStep.STEP_ONE.getCode());
        verification.setStatus(PrivilegeVerificationStatus.IN_PROGRESS.getCode());

        // 设置过期时间
        long expireTime = System.currentTimeMillis() + properties.getExpireMinutes() * 60 * 1000L;
        verification.setExpireTime(expireTime);

        verificationMapper.insert(verification);
        return verification;
    }

    /**
     * 执行步骤一逻辑
     */
    private void executeStepOne(WenUserPrivilegeVerification verification, WenUserPrivilege privilege) {
        // 获取特权模板信息
        final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
        if (template == null) {
            throw new UserException(UserExceptionEnum.USER_PRIVILEGE_TYPE_NOT_EXISTS);
        }

        // 获取特权提供者的用户信息
        final WenUser privilegeProviderUser = userMapper.byId(privilege.getUserId());
        if (privilegeProviderUser == null) {
            throw new UserException(UserExceptionEnum.USER_NOT_EXISTS);
        }

        // 获取用户A信息
        final WenUser userA = userMapper.byId(verification.getUserId());
        if (userA == null) {
            throw new UserException(UserExceptionEnum.USER_NOT_EXISTS);
        }

        // 生成验证页面并发送邮件
        generateVerificationPageAndSendEmail(verification, template, userA, privilegeProviderUser);

        // 更新到步骤二
        verification.setCurrentStep(PrivilegeVerificationStep.STEP_TWO.getCode());
        verificationMapper.updateById(verification);
    }

    /**
     * 生成验证页面并发送邮件
     */
    private void generateVerificationPageAndSendEmail(WenUserPrivilegeVerification verification,
            WenUserPrivilegeTemplate template, WenUser userA, WenUser userB) {

        // 生成验证页面
        String verificationPageUrl = generateVerificationPage(verification, template, userA, userB);
        verification.setPageUrl(verificationPageUrl);

        // 设置Redis过期监听
        String redisKey = PrivilegeVerificationConstant.REDIS_KEY_PREFIX + extractMinioPath(verificationPageUrl);
        SpringRedisUtils.set(redisKey, verification.getId().toString(),
                properties.getExpireMinutes(), TimeUnit.MINUTES);

        // 发送邮件给用户B
        sendVerificationEmail(verification, template, userA, userB, verificationPageUrl);
    }

    /**
     * 从URL中提取MinIO路径
     */
    private String extractMinioPath(String url) {
        // 从完整URL中提取MinIO路径部分
        // 例如：从 http://minio.example.com/privilege/abc123.html 提取 abc123.html
        if (url != null && url.contains("/")) {
            return url.substring(url.lastIndexOf("/") + 1);
        }
        return url;
    }

    /**
     * 生成验证页面
     */
    private String generateVerificationPage(WenUserPrivilegeVerification verification,
            WenUserPrivilegeTemplate template, WenUser userA, WenUser userB) {

        // 获取特权类型
        final WenUserPrivilege privilege = privilegeMapper.selectById(verification.getPrivilegeId());
        PrivilegeVerificationType type = PrivilegeVerificationType.fromCode(privilege.getVerificationType());
        String htmlContent;

        if (type == PrivilegeVerificationType.SMS) {
            htmlContent = generateSmsVerificationPage(verification, template, userA, userB);
        } else {
            htmlContent = generateQrCodeVerificationPage(verification, template, userA, userB);
        }

        // 上传到MinIO
        try {

            // 生成文件名（使用hash）
            byte[] contentBytes = htmlContent.getBytes("UTF-8");
            String hash = EnCodingUtils.calculateHash(new ByteArrayInputStream(contentBytes));
            String object = "verification/" + hash;
            objectStorageTemplate.putObject(
                    BucketConstant.PRIVILEGE,
                    object,
                    new ByteArrayInputStream(contentBytes),
                    contentBytes.length,
                    MediaType.TEXT_HTML_VALUE);

            // 生成访问URL
            return objectStorageTemplate.getPresignedObjectUrl(
                    BucketConstant.PRIVILEGE,
                    object,
                    properties.getExpireMinutes() * 60 // 转换为秒
            );

        } catch (Exception e) {
            log.error("生成验证页面失败", e);
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }
    }

    /**
     * 生成短信验证页面HTML
     */
    private String generateSmsVerificationPage(WenUserPrivilegeVerification verification,
            WenUserPrivilegeTemplate template, WenUser userA, WenUser userB) {

        Map<String, Object> variables = new HashMap<>();
        variables.put("subjectPrefix", template.getName());
        variables.put("userAUsername", userA.getUsername());
        variables.put("templateName", template.getName());
        variables.put("phoneNumber", userB.getPhone());
        variables.put("verificationId", verification.getId());

        return HtmlTemplateUtils.processTemplate(HtmlTemplateUtils.Templates.SMS_VERIFICATION_PAGE, variables);
    }

    /**
     * 生成二维码验证页面HTML
     */
    private String generateQrCodeVerificationPage(WenUserPrivilegeVerification verification,
            WenUserPrivilegeTemplate template, WenUser userA, WenUser userB) {

        Map<String, Object> variables = new HashMap<>();
        variables.put("subjectPrefix", template.getName());
        variables.put("userAUsername", userA.getUsername());
        variables.put("templateName", template.getName());
        variables.put("qrCodeUrl", ""); // 二维码URL需要根据实际情况设置
        variables.put("verificationId", verification.getId());

        return HtmlTemplateUtils.processTemplate(HtmlTemplateUtils.Templates.QRCODE_VERIFICATION_PAGE, variables);
    }

    /**
     * 发送验证邮件
     */
    private void sendVerificationEmail(WenUserPrivilegeVerification verification,
            WenUserPrivilegeTemplate template, WenUser userA, WenUser userB, String verificationPageUrl) {

        // 获取特权类型
        final WenUserPrivilege privilege = privilegeMapper.selectById(verification.getPrivilegeId());
        PrivilegeVerificationType type = PrivilegeVerificationType.fromCode(privilege.getVerificationType());

        boolean success = false;
        if (type == PrivilegeVerificationType.SMS) {
            success = emailService.sendSmsVerificationEmail(userA, userB, template, verificationPageUrl);
        } else if (type == PrivilegeVerificationType.QR_CODE) {
            success = emailService.sendQrCodeVerificationEmail(userA, userB, template, verificationPageUrl);
        }

        if (success) {
            log.info("验证邮件发送成功，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
        } else {
            log.error("验证邮件发送失败，收件人: {}, 特权: {}", userB.getEmail(), template.getName());
        }
    }

    @Override
    public WenUserPrivilegeVerificationResp verificationStatus(Long verificationId) {
        final WenUserPrivilegeVerification verification = verificationMapper.byId(verificationId);
        if (verification == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查是否过期
        if (verification.getExpireTime() < System.currentTimeMillis()) {
            verification.setStatus(PrivilegeVerificationStatus.TIMEOUT.getCode());
            verificationMapper.updateById(verification);
        }

        return toResp(verification);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean submitVerificationContent(Long id, WenUserPrivilegeVerificationSubmitReq req) {
        final String verificationContent = req.getContent();

        final WenUserPrivilegeVerification verification = verificationMapper.byId(id);
        if (verification == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查状态
        if (!verification.getStatus().equals(PrivilegeVerificationStatus.IN_PROGRESS.getCode())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 检查是否过期
        if (verification.getExpireTime() < System.currentTimeMillis()) {
            verification.setStatus(PrivilegeVerificationStatus.TIMEOUT.getCode());
            verificationMapper.updateById(verification);
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_EXPIRED);
        }

        // 验证内容
        if (validateVerificationContent(verification, verificationContent)) {
            verification.setContent(verificationContent);
            verification.setStatus(PrivilegeVerificationStatus.SUCCESS.getCode());
            verification.setCurrentStep(PrivilegeVerificationStep.STEP_THREE.getCode());
            verificationMapper.updateById(verification);
            return true;
        } else {
            verification.setStatus(PrivilegeVerificationStatus.FAILED.getCode());
            verificationMapper.updateById(verification);
            return false;
        }
    }

    /**
     * 验证验证内容
     */
    private boolean validateVerificationContent(WenUserPrivilegeVerification verification, String content) {
        if (!StringUtils.hasText(content)) {
            return false;
        }

        // 获取特权信息
        final WenUserPrivilege privilege = privilegeMapper.selectById(verification.getPrivilegeId());
        if (privilege == null) {
            return false;
        }

        PrivilegeVerificationType type = PrivilegeVerificationType.fromCode(privilege.getVerificationType());

        if (type == PrivilegeVerificationType.SMS) {
            // 短信验证码验证（6位数字）
            return content.matches("\\d{6}");
        } else if (type == PrivilegeVerificationType.QR_CODE) {
            // 二维码内容验证
            final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
            if (template == null || !StringUtils.hasText(template.getQrCodeUrl())) {
                return false;
            }
            // 检查二维码内容是否以模板的qrCodeUrl开头
            return content.startsWith(template.getQrCodeUrl());
        }

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean triggerStepThree(Long verificationId) {
        final WenUserPrivilegeVerification verification = verificationMapper.byId(verificationId);
        if (verification == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查状态
        if (!verification.getStatus().equals(PrivilegeVerificationStatus.IN_PROGRESS.getCode())) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_STATUS_ERROR);
        }

        // 更新到步骤三
        verification.setCurrentStep(PrivilegeVerificationStep.STEP_THREE.getCode());
        verificationMapper.updateById(verification);

        log.info("触发步骤三，验证流程ID: {}", verificationId);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handleExpiredVerifications() {
        List<WenUserPrivilegeVerification> expiredList = verificationMapper.listExpired();
        for (WenUserPrivilegeVerification verification : expiredList) {
            verification.setStatus(PrivilegeVerificationStatus.TIMEOUT.getCode());
            verificationMapper.updateById(verification);

            // 清理MinIO资源
            if (StringUtils.hasText(verification.getPageUrl())) {
                String minioPath = extractMinioPath(verification.getPageUrl());
                cleanupVerificationPage(minioPath);
            }
        }
        log.info("处理过期验证流程数量: {}", expiredList.size());
    }

    @Override
    public void cleanupVerificationPage(String minioPath) {
        try {
            objectStorageTemplate.removeObject(BucketConstant.PRIVILEGE, minioPath);
            log.info("清理MinIO验证页面资源: {}", minioPath);
        } catch (Exception e) {
            log.error("清理MinIO验证页面资源失败: {}", minioPath, e);
        }
    }

    /**
     * 转换为VO
     */
    private WenUserPrivilegeVerificationResp toResp(WenUserPrivilegeVerification verification) {
        final WenUserPrivilegeVerificationResp resp = new WenUserPrivilegeVerificationResp();
        BeanUtils.copyProperties(verification, resp);

        // 获取特权名称
        final WenUserPrivilege privilege = privilegeMapper.selectById(verification.getPrivilegeId());
        if (privilege != null) {
            final WenUserPrivilegeTemplate template = templateMapper.selectById(privilege.getTemplateId());
            if (template != null) {
                resp.setPrivilegeName(template.getName());
                resp.setVerificationType(privilege.getVerificationType());
            }
        }

        return resp;
    }

    @Override
    public WenVerificationTimeInfoResp startVerificationTimer(Long verificationId) {
        WenUserPrivilegeVerification verification = verificationMapper.byId(verificationId);

        if (verification == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 检查是否已经开始计时
        if (verification.getPageAccessTime() != null) {
            // 已经开始计时，返回当前状态
            return remainingTime(verificationId);
        }

        // 开始计时
        long currentTime = System.currentTimeMillis();
        verification.setPageAccessTime(currentTime);
        verification.setAutoCompleteTime(currentTime + 30000); // 30秒后自动完成
        verificationMapper.updateById(verification);

        log.info("启动验证计时器: verificationId={}, startTime={}", verificationId, currentTime);

        WenVerificationTimeInfoResp timeResp = new WenVerificationTimeInfoResp();
        timeResp.setRemainingSeconds(30L);
        timeResp.setAutoCompleted(false);
        timeResp.setStartTime(currentTime);
        timeResp.setAutoCompleteTime(currentTime + 30000);
        timeResp.setStatus(verification.getStatus().toString());
        timeResp.setTimerStarted(true);

        return timeResp;
    }

    @Override
    public WenVerificationTimeInfoResp remainingTime(Long verificationId) {
        WenUserPrivilegeVerification verification = verificationMapper.byId(verificationId);

        if (verification == null) {
            throw new UserException(UserExceptionEnum.PRIVILEGE_VERIFICATION_NOT_EXISTS);
        }

        // 如果还没有开始计时
        if (verification.getPageAccessTime() == null) {
            return startVerificationTimer(verificationId);
        }

        long currentTime = System.currentTimeMillis();
        long autoCompleteTime = verification.getAutoCompleteTime();

        WenVerificationTimeInfoResp timeResp = new WenVerificationTimeInfoResp();
        timeResp.setStartTime(verification.getPageAccessTime());
        timeResp.setAutoCompleteTime(autoCompleteTime);
        timeResp.setStatus(verification.getStatus().toString());
        timeResp.setTimerStarted(true);

        if (currentTime >= autoCompleteTime) {
            // 已超过自动完成时间
            if (!verification.getStatus().equals(PrivilegeVerificationStatus.SUCCESS.getCode())) {
                // 如果还未完成，则自动完成
                autoCompleteVerification(verificationId);
                timeResp.setStatus(PrivilegeVerificationStatus.SUCCESS.getCode().toString());
            }
            timeResp.setRemainingSeconds(0L);
            timeResp.setAutoCompleted(true);
        } else {
            // 计算剩余时间
            long remainingMs = autoCompleteTime - currentTime;
            timeResp.setRemainingSeconds(remainingMs / 1000);
            timeResp.setAutoCompleted(false);
        }

        return timeResp;
    }

    /**
     * 自动完成验证
     */
    private void autoCompleteVerification(Long verificationId) {
        WenUserPrivilegeVerification verification = verificationMapper.byId(verificationId);
        verification.setContent("AUTO_COMPLETED_" + System.currentTimeMillis());
        verification.setStatus(PrivilegeVerificationStatus.SUCCESS.getCode());
        verification.setCurrentStep(PrivilegeVerificationStep.STEP_THREE.getCode());
        verificationMapper.updateById(verification);

        log.info("验证自动完成: verificationId={}", verificationId);
    }
}
