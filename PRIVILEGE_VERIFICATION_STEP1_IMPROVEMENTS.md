# 特权验证步骤1改进说明

## 改进概述

根据需求，对特权验证流程的步骤1进行了以下改进：

1. **添加模板平台链接显示** - 在步骤1中显示对应的特权模板链接
2. **链接点击状态管理** - 只有点击了链接后才显示开始验证按钮
3. **二维码图片上传组件** - 对于二维码验证类型，添加了二维码截图上传功能

## 具体修改内容

### 1. 前端组件修改 (`web/src/components/verification/PrivilegeVerificationModal.vue`)

#### 新增功能：
- **模板平台链接区域**：显示特权模板的链接，引导用户访问对应平台
- **二维码上传区域**：仅在二维码验证类型且点击链接后显示
- **智能按钮显示**：根据用户操作状态动态显示开始验证按钮或提示信息

#### 新增状态管理：
```typescript
// 步骤1相关状态
const linkClicked = ref(false)           // 链接是否被点击
const qrCodeImage = ref<File | null>(null)  // 上传的二维码图片
const qrCodeImageUrl = ref('')           // 二维码预览URL

// 是否显示开始验证按钮的计算属性
const shouldShowStartButton = computed(() => {
  if (!linkClicked.value) return false
  if (props.privilege?.verificationType === 1 && !qrCodeImage.value) return false
  return true
})
```

#### 新增方法：
- `handleLinkClick()` - 处理链接点击事件
- `handleQrCodeUpload()` - 处理二维码图片上传
- 更新了 `resetModal()` 方法以重置新增状态

### 2. 样式文件修改 (`web/src/styles/verification/privilege-verification-modal.scss`)

#### 新增样式类：
- `.template-link-section` - 模板链接区域样式
- `.qrcode-upload-section` - 二维码上传区域样式
- `.start-button-hint` - 开始验证按钮提示样式

#### 样式特点：
- 使用不同的背景色区分不同功能区域
- 响应式设计，适配移动端
- 支持深色主题

## 用户交互流程

### 短信验证流程：
1. 用户看到模板平台链接
2. 点击链接访问对应平台发送验证码
3. 点击链接后，"开始验证"按钮出现
4. 点击开始验证进入步骤2

### 二维码验证流程：
1. 用户看到模板平台链接
2. 点击链接访问对应平台查看登录二维码
3. 点击链接后，出现二维码截图上传区域
4. 用户上传二维码截图
5. 上传完成后，"开始验证"按钮出现
6. 点击开始验证进入步骤2

## 技术实现细节

### 数据流：
1. `PrivilegeResponse` 类型已包含 `link` 字段，无需修改后端API
2. 前端通过 `props.privilege?.link` 获取模板链接
3. 通过 `props.privilege?.verificationType` 判断验证类型

### 状态管理：
- 使用 Vue 3 的 `ref` 和 `computed` 进行响应式状态管理
- 在弹框关闭时自动重置所有状态
- 图片预览URL使用 `URL.createObjectURL()` 创建，并在重置时正确释放

### 组件导入：
- 新增了 `NUpload` 和 `NUploadDragger` 组件
- 新增了 `UploadFileInfo` 类型导入

## 兼容性说明

- 所有修改都是向后兼容的
- 现有的验证流程不受影响
- 如果特权没有链接信息，相关区域不会显示
- 保持了原有的错误处理和状态管理逻辑

## 测试建议

1. **功能测试**：
   - 测试短信验证类型的链接点击和按钮显示
   - 测试二维码验证类型的链接点击、图片上传和按钮显示
   - 测试弹框关闭时的状态重置

2. **边界测试**：
   - 测试没有链接的特权
   - 测试图片上传失败的情况
   - 测试重复上传图片的情况

3. **UI测试**：
   - 测试不同屏幕尺寸下的显示效果
   - 测试深色主题下的样式
   - 测试图片预览的显示效果
